interface NextProps {
  color?: string;
}

export const Next = ({ color = "#D9D9D9", ...props }: NextProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="12"
      fill="none"
      color={color}
      viewBox="0 0 12 12"
      {...props}
    >
      <path
        fill={color}
        fillRule="evenodd"
        d="M3.583 10.292a.71.71 0 0 1-.069-.925l.07-.08L6.87 6 3.583 2.713a.71.71 0 0 1-.069-.925l.07-.08a.71.71 0 0 1 .924-.069l.08.07 3.79 3.789a.71.71 0 0 1 .068.925l-.069.08-3.79 3.789a.71.71 0 0 1-1.004 0"
        clipRule="evenodd"
      ></path>
    </svg>
  );
};
