"use client";

import React, { useEffect, useState, useCallback } from "react";
import { cn } from "@/libs/utils";
import { detectAvailableSections } from "./utils";
import { NavigationChipsProps } from "./types";

export const NavigationChips: React.FC<NavigationChipsProps> = ({
  strapiProduct,
  stickyNavTop: propStickyNavTop,
  className,
}) => {
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [stickyNavTop, setStickyNavTop] = useState(propStickyNavTop || 118);

  // Detect available sections internally
  const sections = detectAvailableSections(strapiProduct);

  // Calculate the exact position of sticky navigation based on actual header and announcement bar heights
  useEffect(() => {
    const calculateStickyPosition = () => {
      const header = document.getElementById("main-header");
      const announcementBar = document.querySelector("[data-no-close]"); // AnnouncementBar has this attribute

      if (header && announcementBar) {
        const headerHeight = (header as HTMLElement).offsetHeight;
        const announcementBarHeight = (announcementBar as HTMLElement)
          .offsetHeight;
        const totalHeight = headerHeight + announcementBarHeight;
        setStickyNavTop(totalHeight);
      } else {
        // Fallback to calculated values if elements not found
        const isMobile = window.innerWidth < 768;
        setStickyNavTop(isMobile ? 64 : 118);
      }
    };

    // Calculate on mount
    calculateStickyPosition();

    // Recalculate on resize
    const handleResize = () => calculateStickyPosition();
    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Smooth scroll to section
  const scrollToSection = useCallback(
    (sectionId: string) => {
      const element = document.getElementById(sectionId);
      if (element) {
        // Set the clicked section as active (manual activation only)
        setActiveSection(sectionId);

        // Calculate offset to account for fixed navigation
        // stickyNavTop includes header + announcement bar height
        // Add NavigationChips height (~60px) + extra padding (40px) to prevent cutting
        const offset = stickyNavTop + 80; // Increased padding to prevent section cutting
        const elementPosition =
          element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - offset;

        window.scrollTo({
          top: offsetPosition,
          behavior: "smooth",
        });
      }
    },
    [stickyNavTop]
  );

  // Handle button click
  const handleButtonClick = (sectionId: string) => {
    scrollToSection(sectionId);
  };

  // Get button styling based on active state
  const getButtonStyles = (sectionId: string) => {
    const isActive = activeSection === sectionId;
    const primaryColor = strapiProduct?.primary_color || "#036A38";

    if (isActive) {
      return {
        backgroundColor: primaryColor,
        color: "white",
      };
    }

    // Inactive buttons use primary color with 30% opacity
    return {
      backgroundColor: `${primaryColor}4D`, // Add "4D" for 30% opacity in hex
      color: "#374151", // gray-700
    };
  };

  const getButtonClasses = (sectionId: string) => {
    const isActive = activeSection === sectionId;

    return cn(
      "px-4 py-2 rounded-[10.5px] text-sm font-medium transition-all duration-200 cursor-pointer ",
      isActive
        ? "border-transparent shadow-sm"
        : "border-gray-200 hover:border-gray-300"
    );
  };

  // Early return if navigation chips should not be shown
  const shouldShow = strapiProduct?.show_navigation_chips;
  const hasSections = sections && sections.length > 0;

  if (!shouldShow || !hasSections) {
    return null;
  }

  return (
    <div
      className={cn("fixed left-0 right-0 z-50 bg-white", className)}
      style={{
        backgroundColor: strapiProduct?.bg_color || "#ffffff",
        top: `${stickyNavTop}px`,
      }}
    >
      <div className="max-w-[1164px] mx-auto px-6">
        <div className="flex items-center gap-4 py-3.5 overflow-x-auto scrollbar-hide">
          {sections.map((section) => (
            <button
              key={section.id}
              className={getButtonClasses(section.id)}
              style={getButtonStyles(section.id)}
              onClick={() => handleButtonClick(section.id)}
              aria-pressed={activeSection === section.id}
              aria-label={`Navigate to ${section.label} section`}
            >
              {section.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
