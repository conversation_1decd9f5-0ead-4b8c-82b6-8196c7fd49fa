import { ProductDetailsType } from "@/types/Collections/ProductDetails";

/**
 * Navigation section interface
 * Represents a single navigation button and its corresponding page section
 */
export interface NavigationSection {
  /** Unique identifier for the section (used for DOM element ID) */
  id: string;
  /** Display label for the navigation button */
  label: string;
  /** Whether this section is currently available/visible */
  available: boolean;
  /** Optional icon or additional metadata */
  metadata?: {
    icon?: string;
    description?: string;
    order?: number;
  };
}

/**
 * Props for the NavigationChips component
 */
export interface NavigationChipsProps {
  /** Strapi product data for theming and configuration */
  strapiProduct?: ProductDetailsType;
  /** Optional top position for sticky navigation (calculated automatically if not provided) */
  stickyNavTop?: number;
  /** Optional additional CSS classes */
  className?: string;
  /** Optional callback when active section changes */
  onActiveSectionChange?: (sectionId: string | null) => void;
  /** Optional callback when a navigation button is clicked */
  onNavigationClick?: (sectionId: string) => void;
}
