import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { NavigationSection } from "./types";

/**
 * Checks for direct component rendering (components not in template.blocks)
 * These are components that are rendered directly in the component tree
 *
 * @param strapiProduct - Strapi product data
 * @returns Object with boolean flags for each direct component
 */
const checkDirectComponentRendering = (strapiProduct?: ProductDetailsType) => {
  console.log("🎯 checkDirectComponentRendering called with:", strapiProduct);
  console.log("🎯 strapiProduct exists:", !!strapiProduct);

  const checks = {
    bestPriceCoupon: false,
    offers: false,
    ingredients: false,
  };

  if (!strapiProduct) {
    console.log("❌ No strapiProduct provided, returning false for all checks");
    return checks;
  }

  // Check for BestPriceCoupon rendering conditions
  // BestPriceCoupon is rendered in ProductActions.tsx and appears to always render
  // when the component is included (no conditional rendering in the component itself)
  // It calculates price from medusaProduct.variants[0], so it needs variants to be meaningful

  // Since BestPriceCoupon is imported and rendered directly in ProductActions.tsx,
  // we can assume it will be rendered for most products. The key indicators are:
  // 1. Product exists (strapiProduct is truthy)
  // 2. This is likely a regular product page (not a bundle-only page)

  // For now, we'll assume BestPriceCoupon is rendered for all products
  // unless there are specific conditions that would prevent it
  const isRegularProduct = !!strapiProduct;
  console.log("🎯 isRegularProduct:", isRegularProduct);

  const productId = (strapiProduct as any)?.id;
  const productSlug = (strapiProduct as any)?.slug;
  const hasProductData = !!productId || !!productSlug;

  console.log("🎯 Product data check:", {
    productId,
    productSlug,
    hasProductData,
  });

  // BestPriceCoupon is rendered directly in ProductActions.tsx for all products
  checks.bestPriceCoupon = isRegularProduct && hasProductData;
  checks.offers = checks.bestPriceCoupon; // Offers section includes BestPriceCoupon

  // WhatsInsideSection (ingredients) is rendered directly in ProductContent.tsx for all products
  // It shows ingredient breakdown and nutritional information
  checks.ingredients = isRegularProduct && hasProductData;

  // TEMPORARY DEBUG: Force offers to true for debugging
  if (strapiProduct) {
    console.log("🚨 DEBUG: Forcing offers to true for debugging");
    checks.offers = true;
    checks.bestPriceCoupon = true;
    checks.ingredients = true;
  }

  console.log("🎯 Final checks calculation:", {
    isRegularProduct,
    hasProductData,
    "isRegularProduct && hasProductData": isRegularProduct && hasProductData,
    bestPriceCoupon: checks.bestPriceCoupon,
    offers: checks.offers,
    ingredients: checks.ingredients,
  });

  console.log("🎯 Direct component checks:", {
    isRegularProduct,
    hasProductData,
    bestPriceCoupon: checks.bestPriceCoupon,
    offers: checks.offers,
    ingredients: checks.ingredients,
    strapiProductKeys: Object.keys(strapiProduct || {}),
  });

  return checks;
};

/**
 * Detects available sections based on specific block types in product data
 *
 * @param strapiProduct - Strapi product data
 * @returns Array of available navigation sections
 */
export const detectAvailableSections = (
  strapiProduct?: ProductDetailsType
): NavigationSection[] => {
  console.log("🔍 detectAvailableSections called with:", strapiProduct);
  console.log("🔍 strapiProduct type:", typeof strapiProduct);
  console.log("🔍 strapiProduct truthy:", !!strapiProduct);

  const availableSections: NavigationSection[] = [];

  // Access blocks from template.blocks path
  const productBlocks = (strapiProduct as any)?.template?.blocks;

  if (!productBlocks || !Array.isArray(productBlocks)) {
    console.log("❌ No blocks found or blocks is not an array");
    console.log("📦 strapiProduct.template.blocks:", productBlocks);
    console.log("📦 strapiProduct.template:", (strapiProduct as any)?.template);
    console.log(
      "🔍 Available strapiProduct properties:",
      Object.keys(strapiProduct || {})
    );
    return availableSections;
  }

  console.log("✅ Found blocks array with length:", productBlocks.length);
  console.log("📋 All blocks:", productBlocks);

  // Log each block's component type
  productBlocks.forEach((block: any, index: number) => {
    console.log(`📄 Block ${index}:`, {
      __typename: block.__typename,
      __component: block.__component,
      type: block.type,
      fullBlock: block,
    });
  });

  // Check for direct component rendering (components not in template.blocks)
  console.log("🔍 Checking for direct component rendering...");
  console.log(
    "🔍 About to call checkDirectComponentRendering with:",
    strapiProduct
  );
  const directComponentChecks = checkDirectComponentRendering(strapiProduct);
  console.log("📋 Direct component detection results:", directComponentChecks);
  console.log("📋 directComponentChecks.offers:", directComponentChecks.offers);
  console.log(
    "📋 directComponentChecks.bestPriceCoupon:",
    directComponentChecks.bestPriceCoupon
  );

  // Define specific sections to look for based on __typename values in template.blocks
  const sectionConfigs = [
    {
      id: "reviews-section",
      label: "Reviews",
      blockTypes: ["ComponentReviewTemplatesVerifiedReviewsTemplate"],
      order: 1,
    },
    {
      id: "faqs-section",
      label: "FAQs",
      blockTypes: ["ComponentPdpTemplatesGotAQuestionTemplate"],
      order: 2,
    },
    {
      id: "offers-section",
      label: "Offers",
      blockTypes: ["ComponentPdpTemplatesOffersTemplate"],
      order: 3,
    },
    {
      id: "certificates-section",
      label: "Certificates",
      blockTypes: ["ComponentPdpTemplatesCertificateBannerTemplate"],
      order: 4,
    },
    {
      id: "ingredients-section",
      label: "Ingredients",
      blockTypes: [
        "ComponentPdpTemplatesIngredientDetailsTemplate",
        "IngredientTemplate",
      ],
      order: 5,
    },
    {
      id: "sachets-section",
      label: "Sachets",
      blockTypes: [
        "ComponentPdpTemplatesSachetsTemplate",
        "SachetsTemplate",
        "SachetTemplate",
      ],
      order: 6,
    },
  ];

  // Check each section configuration against available blocks
  sectionConfigs.forEach((config) => {
    console.log(`🔍 Checking config for ${config.label}:`, config);

    // Check template.blocks for matching components
    const hasMatchingBlock = productBlocks.some((block: any) => {
      const blockType =
        block.__typename || block.__component || block.type || "";
      console.log(
        `🔎 Comparing block type "${blockType}" with expected types:`,
        config.blockTypes
      );

      // Use exact matching for DynamicTemplate component names (check __typename first)
      const isMatch = config.blockTypes.some((type) => blockType === type);
      console.log(`✅ Match found for ${config.label}:`, isMatch);

      return isMatch;
    });

    // Check for direct component rendering (for components not in template.blocks)
    let hasDirectComponent = false;
    if (config.id === "offers-section") {
      console.log(`🔍 Checking direct component for ${config.label}...`);
      console.log(`🔍 directComponentChecks object:`, directComponentChecks);
      console.log(
        `🔍 directComponentChecks.offers:`,
        directComponentChecks.offers
      );
      hasDirectComponent = directComponentChecks.offers;
      console.log(
        `🎯 Direct component check for ${config.label}:`,
        hasDirectComponent
      );
    } else if (config.id === "ingredients-section") {
      console.log(`🔍 Checking direct component for ${config.label}...`);
      console.log(
        `🔍 directComponentChecks.ingredients:`,
        directComponentChecks.ingredients
      );
      hasDirectComponent = directComponentChecks.ingredients;
      console.log(
        `🎯 Direct component check for ${config.label}:`,
        hasDirectComponent
      );
    }

    const shouldIncludeSection = hasMatchingBlock || hasDirectComponent;
    console.log(`🎯 Final result for ${config.label}:`, shouldIncludeSection, {
      hasMatchingBlock,
      hasDirectComponent,
    });

    if (shouldIncludeSection) {
      availableSections.push({
        id: config.id,
        label: config.label,
        available: true,
        metadata: { order: config.order },
      });
    }
  });

  // Sort by order
  return availableSections.sort((a, b) => {
    const orderA = a.metadata?.order || 999;
    const orderB = b.metadata?.order || 999;
    return orderA - orderB;
  });
};

/**
 * Utility to add section IDs to DOM elements
 * This should be called when rendering sections to ensure they have the correct IDs
 */
export const ensureSectionIds = (sections: NavigationSection[]) => {
  sections.forEach((section) => {
    // This is a utility function that components can use to ensure
    // their DOM elements have the correct IDs for navigation
    const element = document.getElementById(section.id);
    if (!element) {
      console.warn(`Navigation section element not found: ${section.id}`);
    }
  });
};
