"use client";

import React, { useState, useRef, useCallback, useEffect } from "react";
import Img from "@/components/Elements/img";
import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogClose,
  DialogPortal,
  DialogOverlay,
} from "@/components/ui/dialog";

interface ProductCarouselProps {
  images: string[];
  title?: string;
  currentSlide: number;
  onSlideChange: (index: number) => void;
  primaryColor?: string;
}

export default function ProductCarousel({
  images,
  title,
  currentSlide,
  onSlideChange,
  primaryColor: propPrimaryColor,
}: ProductCarouselProps) {
  // Use prop color or fallback to default
  const primaryColor = propPrimaryColor || "black";
  const [carouselApi, setCarouselApi] = useState<CarouselApi>();
  const [modalOpen, setModalOpen] = useState(false);
  const [modalCurrentSlide, setModalCurrentSlide] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });

  const thumbnailContainerRef = useRef<HTMLDivElement>(null);
  const modalThumbnailContainerRef = useRef<HTMLDivElement>(null);
  const modalImageRef = useRef<HTMLDivElement>(null);

  // Auto-scroll thumbnail container to center the active thumbnail
  const scrollThumbnailToCenter = useCallback(
    (index: number, containerRef: React.RefObject<HTMLDivElement | null>) => {
      const container = containerRef.current;
      if (!container) {
        console.log("Container not found for scrolling");
        return;
      }

      const thumbnails = container.querySelectorAll("button");
      const targetThumbnail = thumbnails[index];
      if (!targetThumbnail) {
        console.log("Target thumbnail not found at index:", index);
        return;
      }

      // Check if this is the modal container (vertical scrolling) or main container (horizontal scrolling)
      const isModalContainer = containerRef === modalThumbnailContainerRef;
      console.log("Scrolling thumbnail:", {
        index,
        isModalContainer,
        containerHeight: container.clientHeight,
        containerScrollHeight: container.scrollHeight,
      });

      if (isModalContainer) {
        // Vertical scrolling for modal
        const containerHeight = container.clientHeight;
        const thumbnailHeight = targetThumbnail.clientHeight;
        const thumbnailTop = targetThumbnail.offsetTop;

        // Calculate the scroll position to center the thumbnail vertically
        const scrollTop =
          thumbnailTop - containerHeight / 2 + thumbnailHeight / 2;

        // Ensure scroll position is within bounds
        const maxScrollTop = container.scrollHeight - containerHeight;
        const clampedScrollTop = Math.max(0, Math.min(scrollTop, maxScrollTop));

        console.log("Modal scroll calculation:", {
          thumbnailTop,
          containerHeight,
          thumbnailHeight,
          scrollTop,
          clampedScrollTop,
          maxScrollTop,
        });

        container.scrollTo({
          top: clampedScrollTop,
          behavior: "smooth",
        });
      } else {
        // Horizontal scrolling for main carousel
        const containerWidth = container.clientWidth;
        const thumbnailWidth = targetThumbnail.clientWidth;
        const thumbnailLeft = targetThumbnail.offsetLeft;

        // Calculate the scroll position to center the thumbnail horizontally
        const scrollLeft =
          thumbnailLeft - containerWidth / 2 + thumbnailWidth / 2;

        // Ensure scroll position is within bounds
        const maxScrollLeft = container.scrollWidth - containerWidth;
        const clampedScrollLeft = Math.max(
          0,
          Math.min(scrollLeft, maxScrollLeft)
        );

        container.scrollTo({
          left: clampedScrollLeft,
          behavior: "smooth",
        });
      }
    },
    [modalThumbnailContainerRef]
  );

  // Handle slide change from main carousel
  const handleSlideChange = useCallback(
    (index: number) => {
      onSlideChange(index);
      scrollThumbnailToCenter(index, thumbnailContainerRef);
    },
    [onSlideChange, scrollThumbnailToCenter]
  );

  // Handle thumbnail click for main carousel
  const handleThumbnailClick = useCallback(
    (index: number) => {
      carouselApi?.scrollTo(index);
    },
    [carouselApi]
  );

  // Handle modal thumbnail click
  const handleModalThumbnailClick = useCallback(
    (index: number) => {
      console.log("Modal thumbnail clicked, setting slide to:", index);
      setModalCurrentSlide(index);
      // Add a small delay to ensure state is updated before scrolling
      setTimeout(() => {
        scrollThumbnailToCenter(index, modalThumbnailContainerRef);
      }, 50);
    },
    [scrollThumbnailToCenter]
  );

  // Handle image click to open modal
  const handleImageClick = useCallback(() => {
    setModalCurrentSlide(currentSlide);
    setModalOpen(true);
  }, [currentSlide]);

  // Handle mouse move for zoom effect
  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!modalImageRef.current) return;

    const rect = modalImageRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    setZoomPosition({ x, y });
  }, []);

  // Auto-scroll thumbnail when currentSlide changes
  useEffect(() => {
    scrollThumbnailToCenter(currentSlide, thumbnailContainerRef);
  }, [currentSlide, scrollThumbnailToCenter]);

  // Auto-scroll modal thumbnail when modalCurrentSlide changes
  useEffect(() => {
    if (modalOpen) {
      // Add a small delay to ensure DOM is ready
      const timeoutId = setTimeout(() => {
        console.log(
          "Auto-scrolling modal thumbnail to index:",
          modalCurrentSlide
        );
        scrollThumbnailToCenter(modalCurrentSlide, modalThumbnailContainerRef);
      }, 150);
      return () => clearTimeout(timeoutId);
    }
  }, [modalCurrentSlide, modalOpen, scrollThumbnailToCenter]);

  // Debug modal state changes
  useEffect(() => {
    console.log("Modal current slide changed to:", modalCurrentSlide);
  }, [modalCurrentSlide]);

  return (
    <>
      {/* Main Carousel */}
      <div className="px-0 md:px-10 w-full flex items-center justify-center order-1 md:order-2">
        <div className="w-full min-w-[600px] h-full relative">
          <Carousel
            className="w-full relative"
            opts={{ loop: true }}
            setApi={setCarouselApi}
            onSlideChange={handleSlideChange}
          >
            <div className="rounded-lg overflow-hidden">
              <CarouselContent>
                {images.map((image, index) => (
                  <CarouselItem key={index}>
                    <div
                      className="relative aspect-[600/400] w-full flex items-center justify-center cursor-zoom-in"
                      onClick={handleImageClick}
                    >
                      <Img
                        src={image}
                        alt={`${title || "Product"} - Image ${index + 1}`}
                        fill
                        className="object-contain h-full w-full"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </div>

            {/* Dot indicators */}
            <div
              className="flex justify-center gap-2 mt-2.5"
              style={
                {
                  "--active-dot-color": primaryColor,
                  "--inactive-dot-color": `${primaryColor}4D`, // 30% opacity in hex
                } as React.CSSProperties
              }
            >
              <CarouselDots
                className="gap-2 product-carousel-dots"
                baseClassName="transition-all duration-300 w-1.5 h-1.5 rounded-full"
                activeClassName="bg-[var(--active-dot-color)]"
                inactiveClassName="bg-[var(--inactive-dot-color)]"
              />
            </div>

            {/* Thumbnail navigation */}
            <div className="mt-3 flex justify-center">
              <div
                ref={thumbnailContainerRef}
                className="max-w-[400px] overflow-x-auto scrollbar-hide"
              >
                <div className="flex gap-[2.5px] min-w-fit px-2">
                  {images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => handleThumbnailClick(index)}
                      className={`relative w-16 h-16 flex-shrink-0 overflow-hidden border transition-all duration-300 hover:opacity-80 cursor-pointer ${
                        currentSlide === index
                          ? "ring-2 opacity-100"
                          : "border-gray-200 hover:border-gray-300 opacity-40"
                      }`}
                      style={
                        currentSlide === index
                          ? {
                              borderColor: primaryColor,
                              boxShadow: `0 0 0 2px ${primaryColor}33`,
                            }
                          : undefined
                      }
                    >
                      <Img
                        src={image}
                        alt={`${title || "Product"} - Thumbnail ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Navigation buttons */}
            <CarouselPrevious
              className="hidden md:block absolute -left-4 md:-left-7 top-1/2 -translate-y-1/2 bg-transparent hover:bg-transparent border-none !h-8 !w-8 cursor-pointer ring-0 shadow-none"
              icon={
                <ChevronLeft
                  style={{ color: primaryColor, width: "32px", height: "32px" }}
                />
              }
            />
            <CarouselNext
              className="hidden md:block absolute -right-4 md:-right-15 top-1/2 -translate-y-1/2 bg-transparent hover:bg-transparent border-none !h-8 !w-8 cursor-pointer ring-0 shadow-none"
              icon={
                <ChevronRight
                  style={{ color: primaryColor, width: "32px", height: "32px" }}
                />
              }
            />
          </Carousel>
        </div>
      </div>

      {/* Modal */}
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
        <DialogPortal>
          <DialogOverlay className="!z-[9998]" />
          <DialogContent className="max-w-[736px] w-full h-[100vh] max-h-[100vh] p-0 border-none bg-white overflow-hidden !z-[9999] [&>button:not([data-custom-close])]:hidden">
            <DialogClose
              data-custom-close
              className="absolute right-4 top-4 !z-[60] rounded-full opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none bg-white/90 hover:bg-white p-2 shadow-lg"
              style={{ backdropFilter: "blur(4px)" }}
            >
              <X className="h-6 w-6 text-black" />
              <span className="sr-only">Close</span>
            </DialogClose>

            <div className="flex h-full">
              {/* Left sidebar with thumbnails */}
              <div className="w-20 md:w-24 bg-gray-50 p-2 flex flex-col h-full">
                <div
                  ref={modalThumbnailContainerRef}
                  className="flex flex-col gap-2 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 h-full"
                  style={{
                    scrollBehavior: "smooth",
                    maxHeight: "100vh",
                    minHeight: "100vh",
                  }}
                >
                  {images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        console.log("Thumbnail clicked:", index);
                        handleModalThumbnailClick(index);
                      }}
                      className={`relative w-16 h-16 md:w-20 md:h-20 flex-shrink-0 rounded-lg overflow-hidden border-2 transition-all duration-300 hover:opacity-80 ${
                        modalCurrentSlide === index
                          ? "ring-2"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      style={
                        modalCurrentSlide === index
                          ? {
                              borderColor: primaryColor,
                              boxShadow: `0 0 0 2px ${primaryColor}30`,
                            }
                          : undefined
                      }
                    >
                      <Img
                        src={image}
                        alt={`${title || "Product"} - Modal Thumbnail ${
                          index + 1
                        }`}
                        fill
                        className="object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>

              {/* Main image area */}
              <div className="flex-1 relative h-full">
                <div className="relative w-full h-full overflow-hidden bg-white">
                  {/* Custom modal carousel implementation */}
                  <div className="w-full h-full relative">
                    {/* Show only the current image */}
                    <div className="w-full h-full relative">
                      {images.map((image, index) => (
                        <div
                          key={index}
                          className={`absolute inset-0 w-full h-full flex items-center justify-center transition-opacity duration-300 ${
                            index === modalCurrentSlide
                              ? "opacity-100 z-10"
                              : "opacity-0 z-0"
                          }`}
                        >
                          <div
                            ref={
                              index === modalCurrentSlide
                                ? modalImageRef
                                : undefined
                            }
                            className={`relative w-full h-full flex items-center justify-center overflow-hidden bg-white p-8 ${
                              isZoomed && "cursor-zoom-in"
                            }`}
                            onMouseEnter={() => setIsZoomed(true)}
                            onMouseLeave={() => setIsZoomed(false)}
                            onMouseMove={handleMouseMove}
                          >
                            <div
                              className={`relative transition-transform duration-300 ${
                                isZoomed ? "scale-200" : "scale-100"
                              }`}
                              style={{
                                transformOrigin: isZoomed
                                  ? `${zoomPosition.x}% ${zoomPosition.y}%`
                                  : "center",
                                width: "80%",
                                height: "80%",
                                maxWidth: "500px",
                                maxHeight: "500px",
                              }}
                            >
                              <Img
                                src={image}
                                alt={`${title || "Product"} - Modal Image ${
                                  index + 1
                                }`}
                                fill
                                className="object-contain"
                                priority={index === modalCurrentSlide}
                                sizes="(max-width: 736px) 100vw, 500px"
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Modal navigation buttons */}
                    {images.length > 1 && (
                      <>
                        <button
                          onClick={() => {
                            const newIndex =
                              modalCurrentSlide > 0
                                ? modalCurrentSlide - 1
                                : images.length - 1;
                            console.log(
                              "Previous button clicked, changing from",
                              modalCurrentSlide,
                              "to",
                              newIndex
                            );
                            setModalCurrentSlide(newIndex);
                            setTimeout(() => {
                              scrollThumbnailToCenter(
                                newIndex,
                                modalThumbnailContainerRef
                              );
                            }, 50);
                          }}
                          className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-black border-2 border-gray-300 h-10 w-10 cursor-pointer z-50 shadow-xl rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 hover:shadow-2xl"
                          style={{ backdropFilter: "blur(4px)" }}
                        >
                          <ChevronLeft className="h-6 w-6" />
                        </button>
                        <button
                          onClick={() => {
                            const newIndex =
                              modalCurrentSlide < images.length - 1
                                ? modalCurrentSlide + 1
                                : 0;
                            console.log(
                              "Next button clicked, changing from",
                              modalCurrentSlide,
                              "to",
                              newIndex
                            );
                            setModalCurrentSlide(newIndex);
                            setTimeout(() => {
                              scrollThumbnailToCenter(
                                newIndex,
                                modalThumbnailContainerRef
                              );
                            }, 50);
                          }}
                          className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/95 hover:bg-white text-black border-2 border-gray-300 h-10 w-10 cursor-pointer z-50 shadow-xl rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 hover:shadow-2xl"
                          style={{ backdropFilter: "blur(4px)" }}
                        >
                          <ChevronRight className="h-6 w-6" />
                        </button>
                      </>
                    )}

                    {/* Slide indicator */}
                    {images.length > 1 && (
                      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm z-40">
                        {modalCurrentSlide + 1} / {images.length}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
}
