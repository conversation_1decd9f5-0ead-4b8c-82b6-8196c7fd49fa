import FAQAccordion from "@/components/Common/FAQAccordion";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";

const GotAQuestion = ({ productData }: { productData: ProductDetailsType }) => {
  const faqData = productData.faqs;
  const primaryData = productData.primary_color;
  if (!faqData?.show_component) return null;

  return (
    <div id="faqs-section" className="max-w-[840px] mx-auto py-8 md:py-14 px-6">
      <div className="mb-4 text-center space-y-4">
        <h2 className="font-narrow text-[28px] lg:text-4xl font-semibold font-obviously text-black">
          {faqData?.title}
        </h2>
        {faqData?.subtitle && (
          <p className="text-xl font-normal font-gooddog text-[#000000]">
            {faqData?.subtitle}
          </p>
        )}
      </div>

      {/* TODO: need to set border and icon color based on primary color of the product */}
      <FAQAccordion
        borderColor={primaryData}
        iconColor="#44426a"
        items={
          faqData?.faq_items?.map((item, index) => ({
            id: String(index),
            question: item.question || "",
            answer: item.answer || "",
          })) || []
        }
      />
    </div>
  );
};

export default GotAQuestion;
