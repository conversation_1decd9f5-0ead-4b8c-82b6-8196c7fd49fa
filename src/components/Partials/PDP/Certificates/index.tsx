import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/libs/utils";
import { getStrapiUrl } from "@/utils/strapiUrl";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { getGlobalSettings } from "@/libs/middlewareAPIs";

export interface CertificateProps {
  className?: string;
  productData: ProductDetailsType;
}

const CertificatesBanner: React.FC<CertificateProps> = async ({
  className,
  productData,
}) => {
  if (!productData.show_certificates) return null;

  const certificateData = await getGlobalSettings();

  const webBgImage = certificateData?.certificates?.bg_image?.web?.url || [];
  const mobileBgImage =
    certificateData?.certificates?.bg_image?.mobile?.url || [];
  const certificates = certificateData?.certificates?.certificate_items || [];

  const BannerContent = () => (
    <div className="max-w-[1020px] mx-auto mb-3.5">
      {/* Desktop image - hidden on small screens, visible on larger screens */}
      <div className="aspect-[18/9] relative w-full hidden md:block">
        {webBgImage && (
          <Image
            src={
              typeof webBgImage === "string"
                ? webBgImage.startsWith("/uploads")
                  ? getStrapiUrl(webBgImage)
                  : webBgImage
                : ""
            }
            alt={
              certificateData?.certificates?.bg_image?.web?.alternativeText ||
              "Banner image desktop"
            }
            fill
            style={{ objectFit: "cover" }}
            priority
            className="rounded-[8px]"
          />
        )}
        {/* Show certificate icon on the desktop device */}
        <div className="hidden md:flex w-[700px] absolute top-[70%] left-1/2 transform -translate-x-1/2 -translate-y-1/2 items-center justify-between gap-12">
          {certificates.map((certificate, index) => (
            <Link key={index} href={certificate.action_link || "#"}>
              <Image
                src={getStrapiUrl(certificate.web?.url || "")}
                alt={certificate.web?.alternativeText || "Certificate"}
                height={68}
                width={68}
                style={{ objectFit: "cover" }}
              />
            </Link>
          ))}
        </div>
      </div>

      {/* Mobile image - visible on small screens, hidden on larger screens */}
      <div className="aspect-[1/1.3] relative w-full block md:hidden">
        {mobileBgImage && (
          <Image
            src={
              typeof mobileBgImage === "string"
                ? mobileBgImage.startsWith("/uploads")
                  ? getStrapiUrl(mobileBgImage)
                  : mobileBgImage
                : ""
            }
            alt={
              certificateData?.certificates?.bg_image?.mobile
                ?.alternativeText || "Banner image mobile"
            }
            fill
            style={{ objectFit: "cover" }}
            priority
          />
        )}
        {/* Show certificate icon on the mobile device */}
        {/* wrapper 1 */}
        <div className="lg:hidden flex flex-col  absolute right-[30px] top-1/2 transform -translate-y-1/2 items-center justify-between gap-4">
          {certificates.slice(0, 4).map((certificate, index) => (
            <Link key={index} href={certificate.action_link || "#"}>
              <Image
                src={getStrapiUrl(certificate.web?.url || "")}
                alt={certificate.web?.alternativeText || "Certificate"}
                height={68}
                width={68}
                style={{ objectFit: "cover" }}
              />
            </Link>
          ))}
        </div>

        {/* wrapper 2 */}
        <div className="lg:hidden flex absolute bottom-[60px] left-1/2 transform -translate-x-1/2 items-center justify-between gap-12">
          {certificates.slice(0, 2).map((certificate, index) => (
            <Link key={index} href={certificate.action_link || "#"}>
              <Image
                src={getStrapiUrl(certificate.web?.url || "")}
                alt={certificate.web?.alternativeText || "Certificate"}
                height={68}
                width={68}
                style={{ objectFit: "cover" }}
              />
            </Link>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div
      id="certificates-section"
      className={cn("max-w-[1020px] mx-auto", className)}
    >
      <BannerContent />
    </div>
  );
};

export default CertificatesBanner;
