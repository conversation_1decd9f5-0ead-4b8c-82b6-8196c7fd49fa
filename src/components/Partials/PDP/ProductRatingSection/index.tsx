"use client";
import React from "react";
import Link from "next/link";
import { ReviewStar } from "@/assets/icons/ReviewStar";
import ChevronDown from "@/assets/icons/ChevronDown";
import { getStarFillPercentage } from "@/libs/utils";
import { useReviewStats } from "../VerifiedReviews/hooks";

interface ProductRatingSectionProps {
  primaryColor?: string;
  productId: string;
}

const ProductRatingSection: React.FC<ProductRatingSectionProps> = ({
  primaryColor = "#036A38",
  productId,
}) => {
  const { data: stats, isLoading } = useReviewStats({
    productId,
    enabled: !!productId,
  });

  const averageRating = stats?.average_rating || 0;
  const reviewCount = stats?.review_count || 0;

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-start w-1/2 space-y-1.5 pb-4">
        <div className="items-start flex">
          {Array.from({ length: 5 }).map((_, index) => (
            <ReviewStar key={index} color="#E5E7EB" fillPercentage={0} />
          ))}
        </div>
        <p className="text-lg font-bold text-[#44426a] font-obviously">
          Loading...
        </p>
        <p className="text-[13px] leading-5 font-normal text-[#44426a] font-obviously">
          Fetching review data
        </p>
      </div>
    );
  }

  return (
    <div className="mb-3 flex items-start justify-between">
      {/* Star Rating Display */}
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <ReviewStar
            key={star}
            color="#FFC83A"
            fillPercentage={getStarFillPercentage(averageRating, star - 1)}
          />
        ))}
      </div>

      {/* Rating Text and Reviews Link */}
      <div className="flex flex-col">
        <p
          style={{ color: primaryColor }}
          className="self-end text-sm font-semibold leading-5 text-black font-obviously"
        >
          {averageRating.toFixed(1)} out of 5
        </p>
        <Link href={"#reviews-section"}>
          <div className="flex items-center gap-0.5">
            <span
              style={{ color: primaryColor }}
              className="text-sm font-normal text-black font-obviously"
            >
              {reviewCount} verified reviews
            </span>
            <ChevronDown className="h-5 w-5" color={primaryColor} />
          </div>
        </Link>
      </div>
    </div>
  );
};

export default ProductRatingSection;
