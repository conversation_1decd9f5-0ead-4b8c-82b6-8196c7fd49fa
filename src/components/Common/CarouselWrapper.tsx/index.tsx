"use client";

import * as React from "react";
import useEmblaCarousel from "embla-carousel-react";
import { WheelGesturesPlugin } from "embla-carousel-wheel-gestures";
import type { EmblaOptionsType } from "embla-carousel";
import { cn } from "@/libs/utils";

interface CustomProgressBarProps {
  progress: number;
  trackColor?: string;
  thumbColor?: string;
  showProgressBar?: boolean;
  className?: string;
}

const CustomProgressBar: React.FC<CustomProgressBarProps> = ({
  progress,
  trackColor = "#FFF7E7",
  thumbColor = "#888",
  showProgressBar = true,
  className = "",
}) => {
  if (!showProgressBar) return null;

  return (
    <div className={`w-full ${className}`}>
      <div
        className="w-full h-1.5 rounded-full relative overflow-hidden"
        style={{ backgroundColor: trackColor }}
      >
        <div
          className="h-full rounded-full transition-all duration-300 ease-out"
          style={{
            backgroundColor: thumbColor,
            width: `${Math.max(0, Math.min(100, progress * 100))}%`,
          }}
        />
      </div>
    </div>
  );
};

interface UniversalCarouselProps {
  children: React.ReactNode;
  className?: string;
  itemClassName?: string;
  options?: EmblaOptionsType;
  useNativeScrollbar?: boolean;
  hideScrollbar?: boolean;
  gapClassName?: string;
  scrollbarColor?: string;
  scrollBarTrackColor?: string;
  showProgressBar?: boolean;
  progressBarTrackColor?: string;
  progressBarThumbColor?: string;
}

export function UniversalCarousel({
  children,
  className,
  itemClassName,
  options = {
    align: "start",
    skipSnaps: false,
    dragFree: true,
  },
  useNativeScrollbar = true, // Default to native scrollbar
  hideScrollbar = false,
  gapClassName = "gap-5",
  scrollbarColor,
  scrollBarTrackColor,
  showProgressBar = false, // Default to native scrollbar instead of progress bar
  progressBarTrackColor = "#FFF7E7",
  progressBarThumbColor = "#888",
}: UniversalCarouselProps) {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [shouldScroll, setShouldScroll] = React.useState(true);
  const [scrollProgress, setScrollProgress] = React.useState(0);

  // Configure the wheel gestures plugin
  const wheelGesturesPlugin = WheelGesturesPlugin({
    forceWheelAxis: "x",
    wheelEnabled: true,
    wheelToScroll: true,
  });

  // Only include the wheel plugin if scrolling is needed and not using native scrollbar
  const plugins =
    shouldScroll && !useNativeScrollbar ? [wheelGesturesPlugin] : [];

  // Always call useEmblaCarousel hook unconditionally
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      ...options,
      // Enable/disable dragging based on whether content needs scrolling
      watchDrag: shouldScroll,
    },
    plugins
  );

  // Use null references when using native scrollbar
  const effectiveEmblaRef = useNativeScrollbar ? null : emblaRef;
  const effectiveEmblaApi = useNativeScrollbar ? null : emblaApi;

  const onScroll = React.useCallback(() => {
    if (useNativeScrollbar) {
      // For native scrollbar, track progress manually
      if (!containerRef.current) return;
      const contentEl = containerRef.current.querySelector(
        ".carousel-content"
      ) as HTMLElement;
      if (!contentEl) return;

      const scrollLeft = contentEl.scrollLeft;
      const maxScrollLeft = contentEl.scrollWidth - contentEl.clientWidth;
      const progress = maxScrollLeft > 0 ? scrollLeft / maxScrollLeft : 0;
      setScrollProgress(Math.max(0, Math.min(1, progress)));
      return;
    }

    if (!effectiveEmblaApi || !shouldScroll) return;
    const progress = Math.max(
      0,
      Math.min(1, effectiveEmblaApi.scrollProgress())
    );
    setScrollProgress(progress);
  }, [effectiveEmblaApi, shouldScroll, useNativeScrollbar]);

  // Function to check if scrolling is needed
  const checkScrollNeeded = React.useCallback(() => {
    if (!containerRef.current) return;

    if (useNativeScrollbar) {
      // For native scrollbar, we always want to show the scrollbar if content overflows
      const containerEl = containerRef.current;
      const contentEl = containerEl.querySelector(".carousel-content");

      if (contentEl) {
        // Check if content width exceeds container width
        const needsScroll = contentEl.scrollWidth > containerEl.clientWidth;
        setShouldScroll(needsScroll);
      }
      return;
    }

    if (!effectiveEmblaApi) return;

    // Get the container width
    const containerWidth = containerRef.current.clientWidth;

    // Get the total content width (sum of all slides)
    const slides = effectiveEmblaApi.slideNodes();

    // Use a standard gap size for calculation (18px for gap-4.5)
    // This is an approximation as we can't easily extract the exact gap size from the className
    const gapSizeInPx = 18; // Default gap size in pixels

    const totalContentWidth =
      slides.reduce((total, slide) => {
        const slideWidth = slide.offsetWidth;
        return total + slideWidth;
      }, 0) +
      (slides.length - 1) * gapSizeInPx; // Add gaps between slides

    // Determine if scrolling is needed
    const needsScroll = totalContentWidth > containerWidth;

    // Only update state if it's different to avoid unnecessary re-renders
    if (shouldScroll !== needsScroll) {
      setShouldScroll(needsScroll);

      // If content fits, reset scroll position to start
      if (!needsScroll && effectiveEmblaApi) {
        effectiveEmblaApi.scrollTo(0);
        setScrollProgress(0);
      }
    }
  }, [effectiveEmblaApi, shouldScroll, useNativeScrollbar]);

  React.useEffect(() => {
    // Set up resize observer to check if scrolling is needed when window size changes
    const resizeObserver = new ResizeObserver(() => {
      checkScrollNeeded();
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);

      // Initial check if scrolling is needed
      checkScrollNeeded();
    }

    // For native scrollbar, set up scroll event listener
    if (useNativeScrollbar) {
      const contentEl = containerRef.current?.querySelector(
        ".carousel-content"
      ) as HTMLElement;
      if (contentEl) {
        contentEl.addEventListener("scroll", onScroll);
        // Initial progress calculation
        onScroll();
      }

      return () => {
        if (contentEl) {
          contentEl.removeEventListener("scroll", onScroll);
        }
        resizeObserver.disconnect();
      };
    }

    // For Embla carousel
    if (effectiveEmblaApi) {
      // Set up scroll event listener
      effectiveEmblaApi.on("scroll", onScroll);

      // Initial scroll progress
      onScroll();

      // Clean up
      return () => {
        effectiveEmblaApi.off("scroll", onScroll);
        resizeObserver.disconnect();
      };
    }

    // Fallback cleanup
    return () => {
      resizeObserver.disconnect();
    };
  }, [effectiveEmblaApi, onScroll, checkScrollNeeded, useNativeScrollbar]);

  // Convert children to array to work with them
  const childrenArray = React.Children.toArray(children);

  return (
    <div
      className={cn("relative flex flex-col w-full", className)}
      ref={containerRef}
      data-scroll-progress={Math.round(scrollProgress * 100)}
    >
      {useNativeScrollbar ? (
        // Native scrollbar version
        <>
          <div
            className={cn(
              "w-full carousel-content rounded-md",
              shouldScroll
                ? "overflow-x-auto overflow-y-hidden"
                : "overflow-hidden",
              !shouldScroll && "flex flex-wrap justify-center",
              hideScrollbar && "scrollbar-hide" // Only hide scrollbar when explicitly requested
            )}
            style={{
              scrollbarWidth: hideScrollbar ? "none" : "thin", // For Firefox
              msOverflowStyle: hideScrollbar ? "none" : "auto", // For IE and Edge
              WebkitOverflowScrolling: "touch", // For iOS momentum scrolling
              // Add custom scrollbar color if provided and not hiding scrollbar
              ...(scrollbarColor && !hideScrollbar
                ? {
                    // For WebKit browsers (Chrome, Safari)
                    "--scrollbar-thumb-color": scrollbarColor,
                    // For Firefox
                    "--scrollbar-track-color": scrollBarTrackColor || "#FFF7E7",
                    // For Firefox
                    scrollbarColor: `${scrollbarColor} ${
                      scrollBarTrackColor || "#FFF7E7"
                    }`,
                  }
                : !hideScrollbar && {
                    // Default colors when scrollbar is visible but no custom color is provided
                    "--scrollbar-thumb-color": "#888",
                    "--scrollbar-track-color": "#FFF7E7",
                    scrollbarColor: "#888 #FFF7E7",
                  }),
            }}
            data-scrollable={shouldScroll && !hideScrollbar ? "true" : "false"}
            aria-label="Scrollable content"
          >
            <div className={`flex ${gapClassName} min-w-max h-auto`}>
              {childrenArray.map((child, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex-shrink-0 min-w-0 flex justify-center items-center overflow-hidden",
                    itemClassName,
                    !shouldScroll && "flex-grow-0"
                  )}
                >
                  {child}
                </div>
              ))}
            </div>
          </div>

          {/* Custom Progress Bar for Native Scrollbar - only when explicitly requested */}
          {shouldScroll && showProgressBar && (
            <CustomProgressBar
              progress={scrollProgress}
              trackColor={progressBarTrackColor}
              thumbColor={progressBarThumbColor}
              showProgressBar={true}
              className="mt-6"
            />
          )}
        </>
      ) : (
        // Embla carousel version
        <>
          <div
            id="carousel-content"
            className={cn(
              "overflow-x-hidden overflow-y-hidden w-full carousel-content rounded-md",
              !shouldScroll && "overflow-visible",
              hideScrollbar && "scrollbar-hide"
            )}
            ref={effectiveEmblaRef}
            data-scrollable={shouldScroll && !hideScrollbar ? "true" : "false"}
            aria-label={
              shouldScroll
                ? "Scrollable content, swipe or use trackpad to scroll horizontally"
                : "Content"
            }
          >
            <div
              className={cn(
                `flex ${gapClassName} h-auto`,
                !shouldScroll && "flex-wrap justify-center"
              )}
            >
              {childrenArray.map((child, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex-shrink-0 min-w-0 flex justify-center items-center overflow-hidden",
                    itemClassName,
                    // If no scrolling needed, allow items to wrap
                    !shouldScroll && "flex-grow-0"
                  )}
                >
                  {child}
                </div>
              ))}
            </div>
          </div>

          {/* Custom Progress Bar for Embla Carousel - only when explicitly requested */}
          {shouldScroll && showProgressBar && (
            <CustomProgressBar
              progress={scrollProgress}
              trackColor={progressBarTrackColor}
              thumbColor={progressBarThumbColor}
              showProgressBar={true}
              className="mt-6"
            />
          )}
        </>
      )}
    </div>
  );
}
